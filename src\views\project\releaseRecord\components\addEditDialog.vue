<!-- 新增编辑弹窗 -->
<template>
  <div class="addEditDialog">
    <el-dialog :title="dialogTitle" :visible.sync="visible" width="810px" @close="closeDialog">
      <el-form ref="form" :model="formData" :rules="rules" label-width="120px" inline>
        <el-form-item label="项目/任务名称" prop="projectTaskName">
          <el-input v-model="formData.projectTaskName" placeholder="项目/任务名称" clearable style="width: 240px" />
        </el-form-item>
        <el-form-item label="业务类型" prop="businessTypeId">
          <el-select v-model="formData.businessTypeId" clearable filterable style="width: 240px">
            <el-option v-for="item in businessTypeOptions" :key="item.id" :value="item.id" :label="item.name"/>
          </el-select>
        </el-form-item>
        <el-form-item label="成果类型" prop="resultType">
          <el-select v-model="formData.resultType" clearable filterable style="width: 240px">
            <el-option v-for="item in dict.type.project_outcome_types" :key="item.value" :value="item.value" :label="item.label"/>
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" prop="priorityLevel">
          <el-select v-model="formData.priorityLevel" clearable filterable style="width: 240px">
            <el-option value="P1" label="P1"/>
            <el-option value="P2" label="P2"/>
            <el-option value="P3" label="P3"/>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="formData.status" clearable filterable style="width: 240px">
            <el-option value="1" label="未开始"/>
            <el-option value="2" label="进行中"/>
            <el-option value="3" label="已完成"/>
            <el-option value="4" label="已取消"/>
          </el-select>
        </el-form-item>
        <el-form-item label="完成评审时间" prop="milestoneRequirements">
          <el-date-picker v-model="formData.milestoneRequirements" value-format="yyyy-MM-dd" type="date" placeholder="完成评审时间" style="width: 240px">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="完成开发时间" prop="milestoneDevelopment">
          <el-date-picker v-model="formData.milestoneDevelopment" value-format="yyyy-MM-dd" type="date" placeholder="完成开发时间" style="width: 240px">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="完成测试验收时间" prop="milestoneTest">
          <el-date-picker v-model="formData.milestoneTest" value-format="yyyy-MM-dd" type="date" placeholder="完成测试验收时间" style="width: 240px">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="完成上线时间" prop="milestoneOnline">
          <el-date-picker v-model="formData.milestoneOnline" value-format="yyyy-MM-dd" type="date" placeholder="完成上线时间" style="width: 240px">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="需求评审进度" prop="requirementsProgress">
          <el-input-number v-model="formData.requirementsProgress" :min="0" :max="100" :precision="0" style="width: 240px" />
        </el-form-item>
        <el-form-item label="开发进度" prop="developmentProgress">
          <el-input-number v-model="formData.developmentProgress" :min="0" :max="100" :precision="0" style="width: 240px" />
        </el-form-item>
        <el-form-item label="测试验收进度" prop="testProgress">
          <el-input-number v-model="formData.testProgress" :min="0" :max="100" :precision="0" style="width: 240px" />
        </el-form-item>
        <el-form-item label="开发组" prop="devTeams">
          <el-select v-model="formData.devTeams" multiple collapse-tags clearable filterable style="width: 240px">
            <el-option v-for="item in dict.type.project_outcome_dev_dept" :key="item.value" :value="item.value" :label="item.label"/>
          </el-select>
        </el-form-item>
        <el-form-item label="测试组" prop="testTeams">
          <el-select v-model="formData.testTeams" multiple collapse-tags clearable filterable style="width: 240px">
            <el-option v-for="item in dict.type.project_outcome_test_dept" :key="item.value" :value="item.value" :label="item.label"/>
          </el-select>
        </el-form-item>
        <el-form-item label="产品经理" prop="productManagers">
          <el-input v-model="formData.productManagers" placeholder="产品经理，多个用逗号分隔" clearable style="width: 240px" />
        </el-form-item>
        <el-form-item label="开发投入人力" prop="devManpower">
          <el-input-number v-model="formData.devManpower" :min="0" :precision="0" style="width: 240px" />
        </el-form-item>
        <el-form-item label="测试投入人力" prop="testManpower">
          <el-input-number v-model="formData.testManpower" :min="0" :precision="0" style="width: 240px" />
        </el-form-item>
        <el-form-item label="开发工作量" prop="devWorkload">
          <el-input-number v-model="formData.devWorkload" :min="0" :precision="1" style="width: 240px" />
        </el-form-item>
        <el-form-item label="测试工作量" prop="testWorkload">
          <el-input-number v-model="formData.testWorkload" :min="0" :precision="1" style="width: 240px" />
        </el-form-item>
        <el-form-item label="所属业务大类" prop="businessCategoryMajor">
          <el-select v-model="formData.businessCategoryMajor" clearable filterable style="width: 240px">
            <el-option v-for="item in dict.type.project_outcome_business_category_major" :key="item.value" :value="item.value" :label="item.label"/>
          </el-select>
        </el-form-item>
        <el-form-item label="所属业务小类" prop="businessCategoryMinor">
          <el-select v-model="formData.businessCategoryMinor" clearable filterable style="width: 240px">
            <el-option v-for="item in dict.type.project_outcome_business_category_minor" :key="item.value" :value="item.value" :label="item.label"/>
          </el-select>
        </el-form-item>
        <el-form-item label="负责项目经理" prop="projectManagers">
          <el-input v-model="formData.projectManagers" placeholder="负责项目经理，多个用逗号分隔" clearable style="width: 240px" />
        </el-form-item>
        <el-form-item label="完成时间" prop="completionTime">
          <el-date-picker v-model="formData.completionTime" value-format="yyyy-MM-dd" type="date" placeholder="完成时间" style="width: 240px">
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="requirementBackground" class="introduction-item" label-width="100%">
          <template slot="label">
            <span>需求背景</span>
          </template>
          <el-input v-model="formData.requirementBackground" placeholder="需求背景" autocomplete="off" type="textarea" :rows="5" :maxlength="1500" show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm" :loading="btnLoading">保存</el-button>
        <el-button @click="closeDialog">取消</el-button>
      </span>
      </el-dialog>
  </div>
</template>
<script>
import { releaseRecordAdd, releaseRecordEdit } from "@/api/system/releaseRecord"
export default {
  dicts: [
    'project_outcome_types',
    'project_outcome_project_manager',
    'project_outcome_business_category_major',
    'project_outcome_business_category_minor',
    'project_outcome_dev_dept',
    'project_outcome_test_dept',
    'project_outcome_other_dept'
  ],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    dialogTitle: {
      type: String
    },
    dialogData: {
      type: Object
    }
  },
  data() {
    return {
      visible: this.dialogVisible,
      btnLoading: false,
      businessTypeOptions: [], // 业务类型下拉数据
      formData: {
        id: '',
        projectTaskName: '',
        businessTypeId: '',
        resultType: '',
        priorityLevel: '',
        status: '',
        milestoneRequirements: '',
        milestoneDevelopment: '',
        milestoneTest: '',
        milestoneOnline: '',
        requirementsProgress: null,
        developmentProgress: null,
        testProgress: null,
        devTeams: [],
        testTeams: [],
        productManagers: '',
        devManpower: null,
        testManpower: null,
        devWorkload: null,
        testWorkload: null,
        requirementBackground: '',
        businessCategoryMajor: '',
        businessCategoryMinor: '',
        projectManagers: '',
        completionTime: ''
      },
      rules: {
        projectTaskName: [
          { required: true, message: '请输入项目/任务名称', trigger: 'blur' }
        ],
        businessTypeId: [
          { required: true, message: '请选择业务类型', trigger: 'change' }
        ],
        resultType: [
          { required: true, message: '请选择成果类型', trigger: 'change' }
        ],
        priorityLevel: [
          { required: true, message: '请选择优先级', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    dialogVisible (val) {
      this.visible = val
      if (this.visible && this.dialogData.id) {
        this.formData = Object.assign(this.formData, {
          id: this.dialogData.id,
          projectTaskName: this.dialogData.projectTaskName,
          businessTypeId: this.dialogData.businessTypeId,
          resultType: this.dialogData.resultType,
          priorityLevel: this.dialogData.priorityLevel,
          status: this.dialogData.status,
          milestoneRequirements: this.dialogData.milestoneRequirements,
          milestoneDevelopment: this.dialogData.milestoneDevelopment,
          milestoneTest: this.dialogData.milestoneTest,
          milestoneOnline: this.dialogData.milestoneOnline,
          requirementsProgress: this.dialogData.requirementsProgress,
          developmentProgress: this.dialogData.developmentProgress,
          testProgress: this.dialogData.testProgress,
          devTeams: !this.dialogData.devTeams ? [] : this.dialogData.devTeams.split(','),
          testTeams: !this.dialogData.testTeams ? [] : this.dialogData.testTeams.split(','),
          productManagers: this.dialogData.productManagers,
          devManpower: this.dialogData.devManpower,
          testManpower: this.dialogData.testManpower,
          devWorkload: this.dialogData.devWorkload,
          testWorkload: this.dialogData.testWorkload,
          requirementBackground: this.dialogData.requirementBackground,
          businessCategoryMajor: this.dialogData.businessCategoryMajor,
          businessCategoryMinor: this.dialogData.businessCategoryMinor,
          projectManagers: this.dialogData.projectManagers,
          completionTime: this.dialogData.completionTime
        })
      }
    }
  },
  computed: {
  },
  created() {
    this.loadBusinessTypes()
  },
  methods: {
    /** 加载业务类型数据 */
    loadBusinessTypes() {
      // 这里应该调用API获取业务类型数据，暂时使用模拟数据
      this.businessTypeOptions = [
        { id: 1, name: '业务类型1' },
        { id: 2, name: '业务类型2' },
        { id: 3, name: '业务类型3' }
      ]
    },
    /** 取消 */
    closeDialog () {
      this.formData = {
        id: '',
        projectTaskName: '',
        businessTypeId: '',
        resultType: '',
        priorityLevel: '',
        status: '',
        milestoneRequirements: '',
        milestoneDevelopment: '',
        milestoneTest: '',
        milestoneOnline: '',
        requirementsProgress: null,
        developmentProgress: null,
        testProgress: null,
        devTeams: [],
        testTeams: [],
        productManagers: '',
        devManpower: null,
        testManpower: null,
        devWorkload: null,
        testWorkload: null,
        requirementBackground: '',
        businessCategoryMajor: '',
        businessCategoryMinor: '',
        projectManagers: '',
        completionTime: ''
      }
      this.resetForm('form')
      this.$emit('update:dialogVisible', false)
    },
    /** 保存 */
    confirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let params = Object.assign({}, this.formData)
          params.devTeams = params.devTeams.join(',')
          params.testTeams = params.testTeams.join(',')
          let apiName = releaseRecordAdd
          if (this.formData.id) {
            apiName = releaseRecordEdit
          }
          this.btnLoading = true
          apiName(params).then(res => {
            this.btnLoading = false
            if (res.code === 200) {
              this.$message.success(res.msg)
              this.closeDialog()
              this.$emit('callback')
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.introduction-item {
  ::v-deep .el-form-item__label {
    text-align: left;
    margin-left: 20px;
  }
  .el-textarea {
    width: 720px;
    margin-left: 20px;
  }
}
.addEditDialog ::v-deep .el-input__count {
  bottom: -16px;
  line-height: 12px;
}
</style>
