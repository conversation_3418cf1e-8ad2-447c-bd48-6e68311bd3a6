<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="一类指标" prop="primaryIndicator">
        <el-select v-model="queryParams.primaryIndicator" clearable filterable style="width: 180px" @change="changePrimaryIndicator">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in primaryIndicatorOptions" :key="item.code" :value="item.code" :label="item.name"/>
        </el-select>
      </el-form-item>
      <el-form-item label="二类指标" prop="secondaryIndicator">
        <el-select v-model="queryParams.secondaryIndicator" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in secondaryIndicatorOptions" :key="item.code" :value="item.code" :label="item.name" v-if="!queryParams.primaryIndicator || item.primaryIndicator === queryParams.primaryIndicator"/>
        </el-select>
      </el-form-item>
      <el-form-item label="项管审核状态" prop="projectManagerAuditStatus">
        <el-select v-model="queryParams.projectManagerAuditStatus" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.performance_feedback_audit_status" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="最终审核状态" prop="finalAudit">
        <el-select v-model="queryParams.finalAudit" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.performance_feedback_audit_status" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="数据来源" prop="dataSource">
        <el-select v-model="queryParams.dataSource" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.performance_feedback_data_source" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="提交状态" prop="submitStatus">
        <el-select v-model="queryParams.submitStatus" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <template v-for="item in dict.type.performance_feedback_submit_status">
            <el-option :key="item.value" v-if="item.value !== 'NOT_SUBMITTED'" :value="item.value" :label="item.label"/>
          </template>
        </el-select>
      </el-form-item>
      <el-form-item label="所属组" prop="groupId">
        <el-select v-model="queryParams.groupId" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in groupDict" :key="item.deptId" :value="item.deptId" :label="item.deptName"/>
        </el-select>
      </el-form-item>
      <el-form-item label="姓名" prop="nickName">
        <el-input v-model="queryParams.nickName" placeholder="姓名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="绩效级别" prop="recommendedLevel">
        <el-select v-model="queryParams.recommendedLevel" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.performance_level" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="事件id" prop="eventId">
        <el-input v-model="queryParams.eventId" placeholder="事件id" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="创建时间" prop="feedbackTimeRange">
        <el-date-picker v-model="queryParams.feedbackTimeRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="事件发生时间" prop="eventTimeRange">
        <el-date-picker v-model="queryParams.eventTimeRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="提交时间" prop="submitTimeRange">
        <el-date-picker v-model="queryParams.submitTimeRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="项管审核时间" prop="projectManagerAuditTimeRange">
        <el-date-picker v-model="queryParams.projectManagerAuditTimeRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="最终审核时间" prop="finalAuditTimeRange">
        <el-date-picker v-model="queryParams.finalAuditTimeRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery(1)">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-button type="primary" size="mini" @click="handleAudit(null, 'batchFinalAudit')" :disabled="selectedIds.length === 0" v-hasPermi="['system:performanceFeedbackMain:batchFinalAudit']">批量最终审核</el-button>
    </el-row>
    <el-table v-loading="loading" :data="tableData" @selection-change="handleSelectionChange" @sort-change="handleSortChange"
      :row-class-name="tableRowClassName" height="calc(100vh - 420px)" border>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="#" width="55" align="center" />
      <el-table-column label="反馈编码" align="center" prop="feedbackCode" />
      <el-table-column label="创建时间" align="center" prop="feedbackTime" min-width="100" sortable="custom" />
      <el-table-column label="一类指标" align="center" prop="primaryIndicator">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.primaryIndicator, primaryIndicatorOptions, 'code', 'name') }}
        </template>
      </el-table-column>
      <el-table-column label="二类指标" align="center" prop="secondaryIndicator">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.secondaryIndicator, secondaryIndicatorOptions, 'code', 'name') }}
        </template>
      </el-table-column>
      <el-table-column label="事件标题" align="center" prop="eventTitle" min-width="160" />
      <el-table-column label="事件明细" align="center" prop="eventDetail" min-width="160">
        <template slot-scope="scope">
          <div v-if="!scope.row.eventDetail || scope.row.eventDetail.length < 30">
            {{ scope.row.eventDetail }}
          </div>
          <el-tooltip v-else :content="scope.row.eventDetail" placement="top">
            <div class="ellipsis-multiline">
              {{ scope.row.eventDetail }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="事件发生时间" align="center" min-width="160">
        <template slot-scope="scope">
          <span v-if="scope.row.eventStartTime">{{ scope.row.eventStartTime }}</span>
          <span v-if="scope.row.eventEndTime">-{{ scope.row.eventEndTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属组" align="center" prop="performanceFeedbackList[0].groupName" />
      <el-table-column label="推荐绩效级别" align="center" prop="recommendedLevel" min-width="100" />
      <el-table-column label="推荐原因" align="center" prop="recommendedReason" min-width="160">
        <template slot-scope="scope">
          <div v-if="!scope.row.recommendedReason || scope.row.recommendedReason.length < 30">
            {{ scope.row.recommendedReason }}
          </div>
          <el-tooltip v-else :content="scope.row.recommendedReason" placement="top">
            <div class="ellipsis-multiline">
              {{ scope.row.recommendedReason }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" min-width="100" />
      <el-table-column label="数据来源" align="center" prop="dataSource">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.dataSource, dict.type.performance_feedback_data_source) }}
        </template>
      </el-table-column>
      <el-table-column label="提交状态" align="center" prop="submitStatus" min-width="100" sortable="custom">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.submitStatus, dict.type.performance_feedback_submit_status) }}
        </template>
      </el-table-column>
      <el-table-column label="提交时间" align="center" prop="submitTime" min-width="100" sortable="custom" />
      <el-table-column label="提交人" align="center" prop="submitterName" min-width="100" />
      <el-table-column label="项管审核状态" align="center" prop="projectManagerAuditStatus" min-width="130" sortable="custom">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.projectManagerAuditStatus, dict.type.performance_feedback_audit_status) }}
        </template>
      </el-table-column>
      <el-table-column label="项管审核人" align="center" prop="projectManagerAuditor" min-width="100">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.projectManagerAuditor, dict.type.project_outcome_project_manager) }}
        </template>
      </el-table-column>
      <el-table-column label="项管审核时间" align="center" prop="projectManagerAuditTime" min-width="140" sortable="custom" />
      <el-table-column label="项管审核意见" align="center" prop="projectManagerRemark" min-width="160" />
      <el-table-column label="最终审核状态" align="center" prop="finalAudit" min-width="130" sortable="custom">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.finalAudit, dict.type.performance_feedback_audit_status) }}
        </template>
      </el-table-column>
      <el-table-column label="最终审核意见" align="center" prop="finalRemark" min-width="160" />
      <el-table-column label="最终审核时间" align="center" prop="finalAuditTime" min-width="140" sortable="custom" />
      <el-table-column label="事件id" align="center" prop="eventId" min-width="100" />
      <el-table-column label="操作" align="center" width="240" fixed="right" class-name="small-padding fixed-width">
        <template v-slot:default="scope">
          <el-button size="mini" type="text" @click="handleView(scope.row)">查看</el-button>
          <el-button size="mini" type="text" @click="handleEdit(scope.row)" v-hasPermi="['system:performanceFeedbackMain:edit']"
            :disabled="scope.row.submitStatus !== 'PENDING_RESUBMIT' || scope.row.dataSource === 'SYSTEM_GENERATED'">编辑</el-button><!--只有待重提、非系统生成的数据可以编辑-->
          <el-button size="mini" type="text" @click="handleAudit(scope.row, 'managerAudit')" v-hasPermi="['system:performanceFeedbackMain:projectManagerAudit']"
            :disabled="!(scope.row.submitStatus === 'SUBMITTED' && scope.row.projectManagerAuditStatus === 'NOT_AUDITED')">项管审核</el-button><!--待审核数据可进行项管审核-->
          <el-button size="mini" type="text" @click="handleAudit(scope.row, 'finalAudit')" v-hasPermi="['system:performanceFeedbackMain:batchFinalAudit']"
            :disabled="!(scope.row.submitStatus === 'SUBMITTED' && scope.row.finalAudit === 'NOT_AUDITED' && scope.row.projectManagerAuditStatus === 'APPROVED')">最终审核</el-button><!--待审核数据可进行最终审核-->
          <el-button size="mini" type="text" class="text-danger" @click="handleRemove(scope.row)"  v-hasPermi="['system:performanceFeedbackMain:remove']"
            :disabled="scope.row.submitStatus !== 'PENDING_RESUBMIT'">删除</el-button><!--只有待重提数据可以删除-->
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 反馈审核弹窗 -->
    <feedbackAuditDialog :dialogVisible.sync="feedbackAuditDialog.show" :dialogType="feedbackAuditDialog.type" :ids="feedbackAuditDialog.ids" @callback="handleQuery(queryParams.pageNum)"></feedbackAuditDialog>
    <!-- 查看/编辑明细弹窗 -->
    <editItemDialog :dialogVisible.sync="editItemDialog.show" :dialogTitle="editItemDialog.title" :dialogFormData="editItemDialog.form" @callback="handleQuery(1)"></editItemDialog>
  </div>
</template>

<script>
import { performanceFeedbackMainList, performanceFeedbackMainDelete, indicatorList } from "@/api/business/performanceFeedback"
import { deptSelect } from "../../../api/commonBiz"
import feedbackAuditDialog from './components/feedbackAuditDialog.vue'
import editItemDialog from './components/editItemDialog.vue'
export default {
  name: "FeedbackManagementDetail",
  dicts: [
    'performance_feedback_audit_status',
    'performance_feedback_data_source',
    'performance_feedback_submit_status',
    'project_outcome_project_manager',
    'performance_level'
  ],
  components: {
    feedbackAuditDialog,
    editItemDialog
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 一类指标下拉数据
      primaryIndicatorOptions: [],
      // 二类指标下拉数据
      secondaryIndicatorOptions: [],
      // 所属组下拉数据
      groupDict: [],
      // 总条数
      total: 0,
      // 数据库权限表格数据
      tableData: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        primaryIndicator: null,
        secondaryIndicator: null,
        projectManagerAuditStatus: null,
        finalAudit: null,
        dataSource: null,
        submitStatus: null,
        groupId: null,
        nickName: null,
        recommendedLevel: null,
        eventId: null,
        feedbackTimeRange: [], // 创建时间
        eventTimeRange: [], // 事件发生时间
        submitTimeRange: [], // 提交时间
        projectManagerAuditTimeRange: [], // 项管审核时间
        finalAuditTimeRange: [] // 最终审核时间
      },
      selectedIds: [], // 选择行id
      feedbackAuditDialog: { // 审核弹窗
        show: false,
        type: '',
        ids: []
      },
      editItemDialog: { // 新增编辑明细弹窗
        show: false,
        title: '',
        form: {}
      }
    }
  },
  watch: {
    $route: {
      handler(route) {
        if (this.$route.params.params && this.$route.name === 'FeedbackManagementDetail') {
          this.resetForm("queryForm")
          const queryParams = JSON.parse(this.$route.params.params || '{}')
          this.queryParams = Object.assign(this.queryParams, queryParams)
          this.handleQuery()
        }
      },
      deep: true
    }
  },
  created () {
    this.indicatorList()
    this.getDeptList()
  },
  mounted () {
    if (this.$route.params.params && this.$route.name === 'FeedbackManagementDetail') {
      this.resetForm("queryForm")
      const queryParams = JSON.parse(this.$route.params.params || '{}')
      this.queryParams = Object.assign(this.queryParams, queryParams)
    }
    this.handleQuery()
  },
  methods: {
    /** 查询数据库权限列表 */
    getList () {
      if (this.queryParams.feedbackTimeRange && this.queryParams.feedbackTimeRange.length !== 0) { // 创建时间
        this.queryParams.feedbackTimeBegin = this.queryParams.feedbackTimeRange[0]
        this.queryParams.feedbackTimeEnd = this.queryParams.feedbackTimeRange[1]
      } else {
        this.queryParams.feedbackTimeBegin = null
        this.queryParams.feedbackTimeEnd = null
      }
      if (this.queryParams.eventTimeRange && this.queryParams.eventTimeRange.length !== 0) { // 事件发生时间
        this.queryParams.eventStartTime = this.queryParams.eventTimeRange[0]
        this.queryParams.eventEndTime = this.queryParams.eventTimeRange[1]
      } else {
        this.queryParams.eventStartTime = null
        this.queryParams.eventEndTime = null
      }
      if (this.queryParams.submitTimeRange && this.queryParams.submitTimeRange.length !== 0) { // 提交时间
        this.queryParams.submitTimeBegin = this.queryParams.submitTimeRange[0]
        this.queryParams.submitTimeEnd = this.queryParams.submitTimeRange[1]
      } else {
        this.queryParams.submitTimeBegin = null
        this.queryParams.submitTimeEnd = null
      }
      if (this.queryParams.projectManagerAuditTimeRange && this.queryParams.projectManagerAuditTimeRange.length !== 0) { // 项管审核时间
        this.queryParams.projectManagerAuditTimeBegin = this.queryParams.projectManagerAuditTimeRange[0]
        this.queryParams.projectManagerAuditTimeEnd = this.queryParams.projectManagerAuditTimeRange[1]
      } else {
        this.queryParams.projectManagerAuditTimeBegin = null
        this.queryParams.projectManagerAuditTimeEnd = null
      }
      if (this.queryParams.finalAuditTimeRange && this.queryParams.finalAuditTimeRange.length !== 0) { // 最终审核时间
        this.queryParams.finalAuditTimeBegin = this.queryParams.finalAuditTimeRange[0]
        this.queryParams.finalAuditTimeEnd = this.queryParams.finalAuditTimeRange[1]
      } else {
        this.queryParams.finalAuditTimeBegin = null
        this.queryParams.finalAuditTimeEnd = null
      }
      let params = {...this.queryParams}
      delete params.feedbackTimeRange
      delete params.eventTimeRange
      delete params.submitTimeRange
      delete params.projectManagerAuditTimeRange
      delete params.finalAuditTimeRange
      this.loading = true
      performanceFeedbackMainList(params).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.total = res.total
          this.tableData = res.rows
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    /** 搜索按钮操作 */
    handleQuery (page) {
      this.queryParams.pageNum = page || 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    /** 根据value获取字典label */
    getDictLabel (value, dictData, valueKey, labelKey) {
      let dictValueKey = valueKey || 'value'
      let dictLabelKey = labelKey || 'label'
      const item = dictData.find(item => item[dictValueKey] === value)
      return item ? item[dictLabelKey] : value
    },
    /** 一类指标变更 */
    changePrimaryIndicator () {
      this.queryParams.secondaryIndicator = null
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.selectedIds = selection.map(item => item.id)
    },
    /** 查看 */
    handleView (row) {
      this.editItemDialog.title = '查看'
      this.editItemDialog.form = row.performanceFeedbackList[0]
      this.editItemDialog.show = true
    },
    /** 编辑 */
    handleEdit (row) {
      this.editItemDialog.title = '编辑'
      this.editItemDialog.form = {
        id: row.performanceFeedbackList[0].id,
        mainFeedbackId: row.performanceFeedbackList[0].mainFeedbackId,
        eventTitle: row.performanceFeedbackList[0].eventTitle,
        eventStartTime: row.performanceFeedbackList[0].eventStartTime,
        eventEndTime: row.performanceFeedbackList[0].eventEndTime,
        eventDetail: row.performanceFeedbackList[0].eventDetail,
        projectManagerAuditor: row.performanceFeedbackList[0].projectManagerAuditor,
        nickName: row.performanceFeedbackList[0].nickName,
        primaryIndicator: row.performanceFeedbackList[0].primaryIndicator,
        secondaryIndicator: row.performanceFeedbackList[0].secondaryIndicator,
        recommendedLevel: row.performanceFeedbackList[0].recommendedLevel,
        recommendedReason: row.performanceFeedbackList[0].recommendedReason,
      }
      this.editItemDialog.show = true
    },
    /** 删除 */
    handleRemove (row) {
      this.$modal.confirm('是否确认删除？').then(() => {
        return performanceFeedbackMainDelete([row.id])
      }).then(() => {
        this.handleQuery()
        this.$modal.msgSuccess("删除成功")
      })
    },
    /** 项管/最终/批量最终审核 */
    handleAudit (row, type) {
      if (type === 'batchFinalAudit') { // 批量最终审核
        let selectedRows = this.tableData.filter(item => this.selectedIds.indexOf(item.id) >= 0)
        let flag = true
        selectedRows.forEach(rowItem => {
          if (!(rowItem.submitStatus === 'SUBMITTED' && rowItem.finalAudit === 'NOT_AUDITED' && rowItem.projectManagerAuditStatus === 'APPROVED')) {
            flag = false
            return
          }
        })
        if (!flag) {
          this.$modal.msgError('仅限于提交状态为已提交，项管审核状态为同意，最终审核状态为未审核时可审核')
          return
        }
      }
      this.feedbackAuditDialog.type = type
      if (row) {
        this.feedbackAuditDialog.ids = [row.id]
      } else {
        this.feedbackAuditDialog.ids = this.selectedIds
      }
      this.feedbackAuditDialog.show = true
    },
    /** 排序 */
    handleSortChange (column, prop, order) {
      this.queryParams.orderByColumn = column.prop
      this.queryParams.isAsc = column.order
      this.handleQuery()
    },
    /** 一类指标、二类指标下拉数据 */
    indicatorList () {
      indicatorList().then(res => {
        if (res.code === 200) {
          this.primaryIndicatorOptions = res.data
          let secondaryIndicatorOptions = []
          this.primaryIndicatorOptions.forEach(item => {
            secondaryIndicatorOptions.push(...item.secondaryIndicators.map(second => {
              return {
                code: second.code,
                name: second.name,
                primaryIndicator: item.code
              }
            }))
          })
          this.secondaryIndicatorOptions = secondaryIndicatorOptions
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    /** 一类指标变更 */
    changePrimaryIndicator () {
      this.queryParams.secondaryIndicator = null
    },
    /** 查询部门列表 */
    getDeptList() {
      deptSelect().then(response => {
        this.groupDict = response.data
      })
    },
    /** 表格背景色class */
    tableRowClassName ({row, rowIndex}) {
      if (row.submitStatus === 'PENDING_RESUBMIT') { // 待提交-蓝色
        return 'blue-row'
      } else if (row.finalAudit === 'APPROVED') { // 项管审核通过-绿色
        return 'green-row'
      } else if (row.projectManagerAuditStatus === 'APPROVED') { // 项管审核通过-黄色
        return 'yellow-row'
      } else if (row.projectManagerAuditStatus === 'NOT_AUDITED') { // 项管审核未审核-浅红色
        return 'red-row'
      }
    }
  }
}
</script>

<style>
/* 多行省略核心样式 */
.ellipsis-multiline {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; /* 控制显示行数 */
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5; /* 与行高保持一致 */
  max-height: calc(3 * 1.5em); /* 行高*行数 */
  word-break: break-all; /* 允许单词内断行 */
}
.el-tooltip__popper {
  max-width: 600px; /* 可根据实际情况调整 */
}
.el-table .blue-row {
  background-color: #ecf5ff;
}
.el-table .red-row {
  background-color: #fef0f0;
}
.el-table .yellow-row {
  background-color: #fdf6ec;
}
.el-table .green-row {
  background-color: #f0f9eb;
}
</style>
