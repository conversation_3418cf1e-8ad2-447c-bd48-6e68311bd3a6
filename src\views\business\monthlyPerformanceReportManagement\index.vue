<!--月绩效报告管理-->
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="年份" prop="year">
        <el-select v-model="queryParams.year" placeholder="选择年份" >
          <el-option
            v-for="year in yearOptions"
            :key="year"
            :label="year"
            :value="year">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="月份" prop="month">
        <el-select v-model="queryParams.month" clearable>
          <el-option
            v-for="dict in dict.type.month"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属组" prop="groupId">
        <el-select v-model="queryParams.groupId" clearable>
          <el-option :value=null label="全部"></el-option>
          <el-option
            v-for="dict in groupDict"
            :key="dict.deptId"
            :label="dict.deptName"
            :value="dict.deptId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="岗位" prop="roleId">
        <el-select v-model="queryParams.roleId" clearable>
          <el-option
            v-for="dict in dict.type.person_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="姓名" prop="nickName">
        <el-input v-model="queryParams.nickName" clearable />
      </el-form-item>
      <el-form-item label="综合绩效" prop="totalLevel">
        <el-select v-model="queryParams.totalLevel" clearable>
          <el-option :value=null label="全部"></el-option>
          <el-option
            v-for="dict in dict.type.level"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="评审绩效" prop="reviewLevel">
        <el-select v-model="queryParams.reviewLevel" clearable>
          <el-option :value=null label="全部"></el-option>
          <el-option
            v-for="dict in dict.type.level"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="核准绩效" prop="finalLevel">
        <el-select v-model="queryParams.finalLevel" clearable>
          <el-option :value=null label="全部"></el-option>
          <el-option
            v-for="dict in dict.type.level"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否邮件报送" prop="emailSentFlag">
        <el-select v-model="queryParams.emailSentFlag" clearable>
          <el-option :value=null label="全部"></el-option>
          <el-option value="1" label="是"></el-option>
          <el-option value="0" label="否"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          :disabled="multiple"
          v-hasPermi="['system:performance:review']"
          @click="handleBatchPerformance('review')"
        >批量评审绩效</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="multiple"
          v-hasPermi="['system:performance:approval']"
          @click="handleBatchPerformance('approval')"
        >批量核准绩效</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-message"
          size="mini"
          :disabled="multiple"
          @click="sendEmail"
          v-hasPermi="['system:performance:send']"
        >选中绩效发送邮件</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="postList" style="width: 100%;" height="calc(100vh - 320px)" stripe border
      @selection-change="handleSelectionChange" @sort-change="handleSortChange" :cell-style="setCellColor">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="所属组" align="center" prop="groupName" width="140" />
      <el-table-column label="岗位" align="center" prop="role" width="120" />
      <el-table-column label="姓名" align="center" prop="nickName" width="120" />
      <el-table-column label="年" align="center" prop="year" sortable="custom" />
      <el-table-column label="月" align="center" prop="month" sortable="custom" />
      <el-table-column label="工作成果绩效" align="center" width="140" prop="work_achievement_summary_sort" sortable="custom">
        <template slot-scope="scope">
          <el-link type="primary" :underline="false" @click="toFeedbackDetails(scope.row, 'work_achievement')">{{scope.row.workAchievementSummary}}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="工作成果综合绩效" align="center" width="140" prop="work_achievement_sort" sortable="custom">
        <template slot-scope="scope">
          {{scope.row.workAchievement}}
        </template>
      </el-table-column>
      <el-table-column label="工作质量绩效" align="center" width="140" prop="work_quality_summary_sort" sortable="custom">
        <template slot-scope="scope">
          <el-link type="primary" :underline="false" @click="toFeedbackDetails(scope.row, 'work_quality')">{{scope.row.workQualitySummary}}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="工作质量综合绩效" align="center" width="140" prop="work_quality_sort" sortable="custom">
        <template slot-scope="scope">
          {{scope.row.workQuality}}
        </template>
      </el-table-column>
      <el-table-column label="工作协作能力绩效" align="center" width="150" prop="collaboration_ability_summary_sort" sortable="custom">
        <template slot-scope="scope">
          <el-link type="primary" :underline="false" @click="toFeedbackDetails(scope.row, 'collaboration_ability')">{{scope.row.collaborationAbilitySummary}}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="工作协作能力综合绩效" align="center" width="160" prop="collaboration_ability_sort" sortable="custom">
        <template slot-scope="scope">
          {{scope.row.collaborationAbility}}
        </template>
      </el-table-column>
      <el-table-column label="综合建议绩效" align="center" prop="total_level_sort" sortable="custom" width="140">
        <template slot-scope="scope">
          {{scope.row.totalLevel}}
        </template>
      </el-table-column>
      <el-table-column label="评审绩效" align="center" prop="review_level_sort" sortable="custom" width="120">
        <template slot-scope="scope">
          {{scope.row.reviewLevel}}
        </template>
      </el-table-column>
      <el-table-column label="本月核准绩效" align="center" prop="final_level_sort" sortable="custom" width="140">
        <template slot-scope="scope">
          {{scope.row.finalLevel}}
        </template>
      </el-table-column>
      <el-table-column label="核准时间" align="center" prop="approvalTime" sortable="custom" width="160" />
      <el-table-column label="是否邮件报送" align="center" width="140">
        <template slot-scope="scope">
          <span v-if="scope.row.emailSentFlag === '0'">否</span>
          <span v-else-if="scope.row.emailSentFlag === '1'">是</span>
        </template>
      </el-table-column>>
      <el-table-column label="报送时间" align="center" prop="emailSentTime" sortable="custom" width="160" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200" fixed="right">
<!--        v-hasPermi="['system:post:edit']"-->
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-document-checked"
            v-hasPermi="['system:performance:review']"
            @click="handlePerformance('review', scope.row)"
          >评审绩效</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-s-check"
            v-hasPermi="['system:performance:approval']"
            @click="handlePerformance('approval', scope.row)"
          >核准绩效</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 弹窗 -->
    <el-dialog :title="title" :visible.sync="open" width="400px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-form-item :label="form.type === 'review' ? '请选择评审绩效等级' : '请选择最终绩效等级'" prop="level">
          <el-select v-model="form.level" clearable>
            <el-option
              v-for="dict in dict.type.level"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  reviewPerformance,
  approvalPerformance,
  getPerformanceList,
  sendPerformance
} from "@/api/business/monthlyPerformanceReportManagement";
import {deptSelect} from "@/api/commonBiz";

export default {
  name: "MonthlyPerformanceReportManagement",
  dicts: ['month', 'level', 'person_type'],
  data() {
    let month = new Date().getMonth() + 1
    return {
      yearOptions: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        year: new Date().getFullYear(), // 年
        month: month < 10 ? ('0' + month) : month, // 月
        nickName: '', // 姓名
        groupId: null, // 所属组
        roleId: null, // 角色
        totalLevel: null, // 综合绩效等级S/A/B/C/D
        reviewLevel: null, // 评审绩效等级S/A/B/C/D
        finalLevel: null, // 最终绩效等级S/A/B/C/D
        emailSentFlag: null, // 是否邮件报送，0为不发，1为发送
        pageNum: 1,
        pageSize: 10,
        orderByColumn: '', // 排序列
        isAsc: ''	// 排序的方向desc或者asc
      },
      groupDict:[], // 组集合
      // 表单参数
      form: {
        ids: [],
        level: ''
      },
      // 表单校验
      rules: {
        level: [
          { required: true, message: '请选择绩效等级', trigger: 'change' }
        ]
      }
    };
  },
  mounted () {
    const currentYear = new Date().getFullYear();
    for (let i = 0; i < 5; i++) {
      this.yearOptions.push(currentYear - i);
    }
  },
  created() {
    this.getDeptList();
    this.getList();
  },
  methods: {
    /** 查列表 */
    getList() {
      this.loading = true;
      getPerformanceList(this.queryParams).then(response => {
        this.postList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        ids: [],
        level: '',
        type: ''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      if (selection.length > 0) {
        this.multiple = false
      } else {
        this.multiple = true
      }
    },
    // 排序
    handleSortChange (data) {
      this.queryParams.orderByColumn = data.prop
      if (data.order === 'ascending') {
        this.queryParams.isAsc = 'asc'
      } else if (data.order === 'descending') {
        this.queryParams.isAsc = 'desc'
      } else {
        this.queryParams.isAsc = ''
      }
      this.getList()
    },
    /** 批量处理绩效 */
    handleBatchPerformance(type) {
      this.reset();
      this.form.ids = this.ids
      this.open = true;
      this.form.type = type
      if (type === 'review') {
        this.title = "评审结果登记"
      } else {
        this.title = "核准结果登记"
      }
    },
    /** 单个处理绩效 */
    handlePerformance(type, row) {
      this.reset();
      this.form.ids.push(row.id)
      this.open = true;
      this.form.type = type
      if (type === 'review') {
        this.title = "评审结果登记"
      } else {
        this.title = "核准结果登记"
      }
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          let params = {
            ids: this.form.ids,
            level: this.form.level
          }
          if (this.form.type === 'review') {
            reviewPerformance(params).then(response => {
              this.$modal.msgSuccess("评审成功");
              this.open = false;
              this.getList();
            });
          } else {
            approvalPerformance(params).then(response => {
              this.$modal.msgSuccess("核审成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    // 发送邮件
    sendEmail () {
      sendPerformance({ids: this.ids}).then(response => {
        this.$modal.msgSuccess(response.msg);
        this.getList();
      });
    },
    // 跳转至反馈明细
    toFeedbackDetails (row, type) {
      let params = {
        primaryIndicator: type,
        projectManagerAuditStatus: 'APPROVED',
        finalAudit: 'APPROVED',
        nickName: row.nickName,
        year: row.year,
        month: row.month
      }
      this.$router.push({
        name: 'FeedbackDetails',
        params: { params: JSON.stringify(params) }
      })
    },
    /** 查询部门列表 */
    getDeptList() {
      deptSelect().then(response => {
        this.groupDict = response.data
      })
    },
    /** 设置单元格颜色 */
    setCellColor({ row, column, rowIndex, columnIndex }) {
      if (column.label === '工作成果综合绩效') {
        if (['S', 'A'].indexOf(row.workAchievement) >= 0) {
          return { backgroundColor: '#f0f9eb' }
        } else if (['C', 'D'].indexOf(row.workAchievement) >= 0) {
          return { backgroundColor: '#fef0f0' }
        }
      }
      if (column.label === '工作质量综合绩效') {
        if (['S', 'A'].indexOf(row.workQuality) >= 0) {
          return { backgroundColor: '#f0f9eb' }
        } else if (['C', 'D'].indexOf(row.workQuality) >= 0) {
          return { backgroundColor: '#fef0f0' }
        }
      }
      if (column.label === '工作协作能力综合绩效') {
        if (['S', 'A'].indexOf(row.collaborationAbility) >= 0) {
          return { backgroundColor: '#f0f9eb' }
        } else if (['C', 'D'].indexOf(row.collaborationAbility) >= 0) {
          return { backgroundColor: '#fef0f0' }
        }
      }
      if (column.label === '综合建议绩效') {
        if (['S', 'A'].indexOf(row.totalLevel) >= 0) {
          return { backgroundColor: '#f0f9eb' }
        } else if (['C', 'D'].indexOf(row.totalLevel) >= 0) {
          return { backgroundColor: '#fef0f0' }
        }
      }
    }
  }
};
</script>
