<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="visible"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body>
    
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      size="small">
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="项目/任务名称" prop="projectTaskName">
            <el-input v-model="form.projectTaskName" placeholder="请输入项目/任务名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="业务类型" prop="businessType">
            <el-select v-model="form.businessType" placeholder="请选择业务类型" style="width: 100%">
              <el-option v-for="item in businessTypeOptions" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="成果类型" prop="resultType">
            <el-select v-model="form.resultType" placeholder="请选择成果类型" style="width: 100%">
              <el-option v-for="item in resultTypeOptions" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
              <el-option v-for="item in statusOptions" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="form.priority" placeholder="请选择优先级" style="width: 100%">
              <el-option label="P1" value="P1" />
              <el-option label="P2" value="P2" />
              <el-option label="P3" value="P3" />
              <el-option label="P4" value="P4" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属业务大类" prop="businessCategoryMajor">
            <el-select v-model="form.businessCategoryMajor" placeholder="请选择业务大类" style="width: 100%">
              <el-option v-for="item in businessCategoryMajorOptions" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属业务小类" prop="businessCategoryMinor">
            <el-select v-model="form.businessCategoryMinor" placeholder="请选择业务小类" style="width: 100%">
              <el-option v-for="item in businessCategoryMinorOptions" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="需求背景" prop="requirementBackground">
            <el-input v-model="form.requirementBackground" placeholder="请输入需求背景" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="任务说明" prop="taskDescription">
        <el-input
          v-model="form.taskDescription"
          type="textarea"
          :rows="3"
          placeholder="请输入任务说明" />
      </el-form-item>
      
      <!-- 干系人信息 -->
      <el-divider content-position="left">干系人信息</el-divider>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="产品负责人" prop="stakeholders.product">
            <el-input v-model="form.stakeholders.product" placeholder="请输入产品负责人" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="开发负责人" prop="stakeholders.development">
            <el-input v-model="form.stakeholders.development" placeholder="请输入开发负责人" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="测试负责人" prop="stakeholders.test">
            <el-input v-model="form.stakeholders.test" placeholder="请输入测试负责人" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 投入人力 -->
      <el-divider content-position="left">投入人力</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开发人力" prop="manpower.development">
            <el-input v-model="form.manpower.development" placeholder="请输入开发投入人力">
              <template slot="append">人</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="测试人力" prop="manpower.test">
            <el-input v-model="form.manpower.test" placeholder="请输入测试投入人力">
              <template slot="append">人</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 工作量 -->
      <el-divider content-position="left">工作量</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开发工作量" prop="workload.development">
            <el-input v-model="form.workload.development" placeholder="请输入开发工作量">
              <template slot="append">人日</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="测试工作量" prop="workload.test">
            <el-input v-model="form.workload.test" placeholder="请输入测试工作量">
              <template slot="append">人日</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 进度信息 -->
      <el-divider content-position="left">进度信息</el-divider>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="需求评审进度" prop="progress.requirementReview">
            <el-input-number
              v-model="form.progress.requirementReview"
              :min="0"
              :max="100"
              style="width: 100%"
              controls-position="right">
            </el-input-number>
            <span style="margin-left: 8px;">%</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="开发进度" prop="progress.development">
            <el-input-number
              v-model="form.progress.development"
              :min="0"
              :max="100"
              style="width: 100%"
              controls-position="right">
            </el-input-number>
            <span style="margin-left: 8px;">%</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="测试验收进度" prop="progress.testing">
            <el-input-number
              v-model="form.progress.testing"
              :min="0"
              :max="100"
              style="width: 100%"
              controls-position="right">
            </el-input-number>
            <span style="margin-left: 8px;">%</span>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 项目里程碑 -->
      <el-divider content-position="left">项目里程碑</el-divider>
      <el-form-item label="里程碑">
        <el-table :data="form.milestones" style="width: 100%" size="small" border>
          <el-table-column label="里程碑名称" width="200">
            <template slot-scope="scope">
              <el-input v-model="scope.row.text" placeholder="请输入里程碑名称" size="small" />
            </template>
          </el-table-column>
          <el-table-column label="完成日期" width="200">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.date"
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
                size="small"
                style="width: 100%">
              </el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" align="center">
            <template slot-scope="scope">
              <el-button 
                type="text" 
                size="small" 
                @click="removeMilestone(scope.$index)"
                style="color: #f56c6c;">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-button type="text" size="small" @click="addMilestone" style="margin-top: 10px;">
          <i class="el-icon-plus"></i> 添加里程碑
        </el-button>
      </el-form-item>
      
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="submitForm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "AddEditDialog",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    dialogTitle: {
      type: String,
      default: '新增'
    },
    dialogData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      visible: false,
      form: {
        projectTaskName: '',
        businessType: '',
        resultType: '',
        status: '',
        priority: '',
        businessCategoryMajor: '',
        businessCategoryMinor: '',
        requirementBackground: '',
        taskDescription: '',
        stakeholders: {
          product: '',
          development: '',
          test: ''
        },
        manpower: {
          development: '',
          test: ''
        },
        workload: {
          development: '',
          test: ''
        },
        progress: {
          requirementReview: 0,
          development: 0,
          testing: 0
        },
        milestones: []
      },
      rules: {
        projectTaskName: [
          { required: true, message: '请输入项目/任务名称', trigger: 'blur' }
        ],
        businessType: [
          { required: true, message: '请选择业务类型', trigger: 'change' }
        ],
        resultType: [
          { required: true, message: '请选择成果类型', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        priority: [
          { required: true, message: '请选择优先级', trigger: 'change' }
        ]
      },
      // 选项数据（静态数据，后续替换为动态获取）
      businessTypeOptions: [
        { value: "software", label: "软件" },
        { value: "finance", label: "金融" },
        { value: "ecommerce", label: "电商" }
      ],
      resultTypeOptions: [
        { value: "system", label: "新系统" },
        { value: "optimization", label: "重要优化" },
        { value: "feature", label: "新功能" }
      ],
      statusOptions: [
        { value: "completed", label: "已完成" },
        { value: "in_progress", label: "进行中" },
        { value: "pending", label: "待开始" }
      ],
      businessCategoryMajorOptions: [
        { value: "1", label: "国内" },
        { value: "2", label: "国外" }
      ],
      businessCategoryMinorOptions: [
        { value: "1", label: "综合" },
        { value: "2", label: "专项" }
      ]
    }
  },
  watch: {
    dialogVisible(val) {
      this.visible = val
      if (val) {
        this.initForm()
      }
    },
    visible(val) {
      this.$emit('update:dialogVisible', val)
    },
    dialogData: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.initForm()
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    /** 初始化表单 */
    initForm() {
      if (this.dialogData && Object.keys(this.dialogData).length > 0) {
        this.form = {
          ...this.form,
          ...this.dialogData,
          stakeholders: { ...this.form.stakeholders, ...this.dialogData.stakeholders },
          manpower: { ...this.form.manpower, ...this.dialogData.manpower },
          workload: { ...this.form.workload, ...this.dialogData.workload },
          progress: { ...this.form.progress, ...this.dialogData.progress },
          milestones: this.dialogData.milestones ? [...this.dialogData.milestones] : []
        }
      } else {
        this.resetForm()
      }
    },
    
    /** 重置表单 */
    resetForm() {
      this.form = {
        projectTaskName: '',
        businessType: '',
        resultType: '',
        status: '',
        priority: '',
        businessCategoryMajor: '',
        businessCategoryMinor: '',
        requirementBackground: '',
        taskDescription: '',
        stakeholders: {
          product: '',
          development: '',
          test: ''
        },
        manpower: {
          development: '',
          test: ''
        },
        workload: {
          development: '',
          test: ''
        },
        progress: {
          requirementReview: 0,
          development: 0,
          testing: 0
        },
        milestones: []
      }
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
      })
    },
    
    /** 添加里程碑 */
    addMilestone() {
      this.form.milestones.push({
        text: '',
        date: ''
      })
    },
    
    /** 删除里程碑 */
    removeMilestone(index) {
      this.form.milestones.splice(index, 1)
    },
    
    /** 提交表单 */
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // TODO: 调用保存API
          console.log('提交表单数据:', this.form)
          this.$message.success('保存成功')
          this.cancel()
          this.$emit('callback')
        } else {
          this.$message.error('请完善必填信息')
          return false
        }
      })
    },
    
    /** 取消 */
    cancel() {
      this.visible = false
      this.resetForm()
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.el-divider {
  margin: 20px 0;
}

.el-divider--horizontal {
  margin: 20px 0;
}
</style>
