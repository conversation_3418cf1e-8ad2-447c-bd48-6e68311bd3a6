<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="900px"
    :before-close="handleClose"
    append-to-body>
    
    <div v-loading="loading" class="detail-content">
      <div class="detail-row">
        <div class="detail-item">
          <label class="detail-label">业务类型</label>
          <div class="detail-value">{{ detailData.businessTypeName || '-' }}</div>
        </div>
        <div class="detail-item">
          <label class="detail-label">状态</label>
          <div class="detail-value">
            <el-tag :type="getStatusType(detailData.status)" size="mini">
              {{ getDictLabel(detailData.status, dict.type.project_outcome_status) }}
            </el-tag>
          </div>
        </div>
        <div class="detail-item">
          <label class="detail-label">优先级</label>
          <div class="detail-value">
            <el-tag :type="getPriorityType(detailData.priorityLevel)" size="mini">
              {{ detailData.priorityLevel || '-' }}
            </el-tag>
          </div>
        </div>
        <div class="detail-item">
          <label class="detail-label">完成时间</label>
          <div class="detail-value">{{ detailData.completionTime | parseTime('{y}-{m}-{d} {h}:{i}:{s}') || '-' }}</div>
        </div>
      </div>

      <div class="detail-row">
        <div class="detail-item full-width">
          <label class="detail-label">项目里程碑</label>
          <div class="detail-value milestone-content">
            <div v-if="detailData.milestoneRequirements">
              完成评审: {{ detailData.milestoneRequirements | parseTime('{y}-{m}-{d}') }}
            </div>
            <div v-if="detailData.milestoneDevelopment">
              完成开发: {{ detailData.milestoneDevelopment | parseTime('{y}-{m}-{d}') }}
            </div>
            <div v-if="detailData.milestoneTest">
              完成测试: {{ detailData.milestoneTest | parseTime('{y}-{m}-{d}') }}
            </div>
            <div v-if="detailData.milestoneOnline">
              完成上线: {{ detailData.milestoneOnline | parseTime('{y}-{m}-{d}') }}
            </div>
            <div v-if="!detailData.milestoneRequirements && !detailData.milestoneDevelopment && !detailData.milestoneTest && !detailData.milestoneOnline">-</div>
          </div>
        </div>
      </div>

      <div class="detail-row">
        <div class="detail-item full-width">
          <label class="detail-label">任务说明/进度</label>
          <div class="detail-value progress-content">
            <div v-if="detailData.requirementsProgress !== null && detailData.requirementsProgress !== undefined">
              需求评审: {{ detailData.requirementsProgress }}%
            </div>
            <div v-if="detailData.developmentProgress !== null && detailData.developmentProgress !== undefined">
              开发进度: {{ detailData.developmentProgress }}%
            </div>
            <div v-if="detailData.testProgress !== null && detailData.testProgress !== undefined">
              测试验收: {{ detailData.testProgress }}%
            </div>
            <div v-if="detailData.requirementsProgress === null && detailData.developmentProgress === null && detailData.testProgress === null">-</div>
          </div>
        </div>
      </div>

      <div class="detail-row">
        <div class="detail-item">
          <label class="detail-label">干系人</label>
          <div class="detail-value stakeholder-content">
            <div v-if="detailData.productTeams">产品: {{ detailData.productTeams }}</div>
            <div v-if="detailData.devTeams">开发: {{ detailData.devTeams }}</div>
            <div v-if="detailData.testTeams">测试: {{ detailData.testTeams }}</div>
            <div v-if="!detailData.productTeams && !detailData.devTeams && !detailData.testTeams">-</div>
          </div>
        </div>
        <div class="detail-item">
          <label class="detail-label">投入人力</label>
          <div class="detail-value manpower-content">
            <div v-if="detailData.devManpower">开发: {{ detailData.devManpower }}人</div>
            <div v-if="detailData.testManpower">测试: {{ detailData.testManpower }}人</div>
            <div v-if="!detailData.devManpower && !detailData.testManpower">-</div>
          </div>
        </div>
      </div>

      <div class="detail-row">
        <div class="detail-item">
          <label class="detail-label">工作量(人日)</label>
          <div class="detail-value workload-content">
            <div v-if="detailData.devWorkload">开发: {{ detailData.devWorkload }}人日</div>
            <div v-if="detailData.testWorkload">测试: {{ detailData.testWorkload }}人日</div>
            <div v-if="!detailData.devWorkload && !detailData.testWorkload">-</div>
          </div>
        </div>
        <div class="detail-item">
          <label class="detail-label">负责项目经理</label>
          <div class="detail-value">{{ detailData.projectManagers || '-' }}</div>
        </div>
      </div>

      <div class="detail-row">
        <div class="detail-item">
          <label class="detail-label">所属业务大类</label>
          <div class="detail-value">{{ getDictLabel(detailData.businessCategoryMajor, dict.type.project_outcome_business_category_major) || '-' }}</div>
        </div>
        <div class="detail-item">
          <label class="detail-label">所属业务小类</label>
          <div class="detail-value">{{ getDictLabel(detailData.businessCategoryMinor, dict.type.project_outcome_business_category_minor) || '-' }}</div>
        </div>
      </div>

      <div class="detail-row">
        <div class="detail-item">
          <label class="detail-label">成果类型</label>
          <div class="detail-value">{{ getDictLabel(detailData.resultType, dict.type.project_outcome_types) || '-' }}</div>
        </div>
        <div class="detail-item">
          <label class="detail-label">创建人</label>
          <div class="detail-value">{{ detailData.createdBy || '-' }}</div>
        </div>
      </div>

      <div class="detail-row">
        <div class="detail-item">
          <label class="detail-label">创建时间</label>
          <div class="detail-value">{{ detailData.createdTime | parseTime('{y}-{m}-{d} {h}:{i}:{s}') || '-' }}</div>
        </div>
        <div class="detail-item">
          <label class="detail-label">更新时间</label>
          <div class="detail-value">{{ detailData.updatedTime | parseTime('{y}-{m}-{d} {h}:{i}:{s}') || '-' }}</div>
        </div>
      </div>

      <div class="detail-row">
        <div class="detail-item full-width">
          <label class="detail-label">需求背景</label>
          <div class="detail-value">{{ detailData.requirementBackground || '-' }}</div>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { projectResultDetail } from "@/api/project/projectResult"

export default {
  name: "DetailDialog",
  dicts: [
    'project_outcome_types',
    'project_outcome_status',
    'project_outcome_priority_level',
    'project_outcome_business_category_major',
    'project_outcome_business_category_minor',
    'project_outcome_project_manager'
  ],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    dialogTitle: {
      type: String,
      default: '项目成果详情'
    },
    recordId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      loading: false,
      detailData: {}
    }
  },
  watch: {
    dialogVisible(val) {
      if (val && this.recordId) {
        this.getDetailData()
      }
    },
    recordId(val) {
      if (val && this.dialogVisible) {
        this.getDetailData()
      }
    }
  },
  methods: {
    /** 获取详情数据 */
    getDetailData() {
      if (!this.recordId) return
      
      this.loading = true
      projectResultDetail(this.recordId).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.detailData = res.data || {}
        } else {
          this.$message.error(res.msg || '获取详情失败')
        }
      }).catch(() => {
        this.loading = false
        this.$message.error('获取详情失败')
      })
    },
    
    /** 关闭弹窗 */
    handleClose() {
      this.$emit('update:dialogVisible', false)
      this.detailData = {}
    },
    
    /** 获取状态类型 */
    getStatusType(status) {
      const typeMap = {
        '1': 'info',
        '2': 'warning', 
        '3': 'success',
        '4': 'danger'
      }
      return typeMap[status] || 'info'
    },
    
    /** 获取优先级类型 */
    getPriorityType(priority) {
      const typeMap = {
        'P1': 'danger',
        'P2': 'warning',
        'P3': 'info'
      }
      return typeMap[priority] || 'info'
    },
    
    /** 根据value获取字典label */
    getDictLabel(value, dictData) {
      if (!value || !dictData) return value
      const item = dictData.find(item => item.value === value)
      return item ? item.label : value
    }
  }
}
</script>

<style scoped>
.detail-content {
  padding: 20px;
}

.detail-row {
  display: flex;
  margin-bottom: 20px;
  gap: 20px;
}

.detail-item {
  flex: 1;
  display: flex;
  align-items: flex-start;
}

.detail-item.full-width {
  flex: 1 1 100%;
  flex-direction: column;
}

.detail-label {
  min-width: 100px;
  font-weight: bold;
  color: #606266;
  margin-right: 10px;
  line-height: 1.5;
}

.detail-item.full-width .detail-label {
  margin-bottom: 8px;
}

.detail-value {
  flex: 1;
  color: #303133;
  line-height: 1.5;
  word-break: break-all;
}

.milestone-content div,
.progress-content div,
.stakeholder-content div,
.manpower-content div,
.workload-content div {
  margin-bottom: 4px;
}

.milestone-content div:last-child,
.progress-content div:last-child,
.stakeholder-content div:last-child,
.manpower-content div:last-child,
.workload-content div:last-child {
  margin-bottom: 0;
}

.dialog-footer {
  text-align: center;
}
</style>
