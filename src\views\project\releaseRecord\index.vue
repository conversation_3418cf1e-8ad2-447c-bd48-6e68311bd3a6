<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="成果类型" prop="resultType">
        <el-select v-model="queryParams.resultType" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.project_outcome_types" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="所属业务大类" prop="businessCategoryMajor">
        <el-select v-model="queryParams.businessCategoryMajor" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.project_outcome_business_category_major" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="所属业务小类" prop="businessCategoryMinor">
        <el-select v-model="queryParams.businessCategoryMinor" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.project_outcome_business_category_minor" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="负责项目经理" prop="projectManager">
        <el-select v-model="queryParams.projectManager" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.project_outcome_project_manager" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="数据来源" prop="dataSource">
        <el-select v-model="queryParams.dataSource" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.project_outcome_data_sources" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="创建人" prop="createdBy">
        <el-select v-model="queryParams.createdBy" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.project_outcome_creator" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createdTimeRange">
        <el-date-picker v-model="queryParams.createdTimeRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="开始时间-结束时间" prop="startEndTimeRange">
        <el-date-picker v-model="queryParams.startEndTimeRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-button type="primary" size="mini" @click="handleAdd">添加</el-button>
    </el-row>
    <el-table v-loading="loading" :data="tableData" @sort-change="handleSortChange" height="calc(100vh - 320px)" stripe border>
      <el-table-column type="index" label="#" width="55" align="center" />
      <el-table-column label="项目/任务名称" align="center" prop="projectTaskName" min-width="120" />
      <el-table-column label="业务类型" align="center" prop="businessTypeName" min-width="100" />
      <el-table-column label="成果类型" align="center" prop="resultType" min-width="100">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.resultType, dict.type.project_outcome_types) }}
        </template>
      </el-table-column>
      <el-table-column label="数据来源" align="center" prop="dataSource" sortable="custom">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.dataSource, dict.type.project_outcome_data_sources) }}
        </template>
      </el-table-column>
      <el-table-column label="所属业务大类" align="center" prop="businessCategoryMajor" sortable="custom">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.businessCategoryMajor, dict.type.project_outcome_business_category_major) }}
        </template>
      </el-table-column>
      <el-table-column label="所属业务小类" align="center" prop="businessCategoryMinor">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.businessCategoryMinor, dict.type.project_outcome_business_category_minor) }}
        </template>
      </el-table-column>
      <el-table-column label="优先级" align="center" prop="priorityLevel" min-width="80" />
      <el-table-column label="状态" align="center" prop="status" min-width="80">
        <template slot-scope="scope">
          {{ getStatusLabel(scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column label="项目里程碑" align="center" min-width="160">
        <template slot-scope="scope">
          <div style="white-space: pre-line; text-align: left;">{{ formatMilestone(scope.row) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="任务说明/进度" align="center" min-width="140">
        <template slot-scope="scope">
          <div style="white-space: pre-line; text-align: left;">{{ formatProgress(scope.row) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="干系人" align="center" min-width="160">
        <template slot-scope="scope">
          <div style="white-space: pre-line; text-align: left;">{{ formatStakeholders(scope.row) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="投入人力" align="center" min-width="100">
        <template slot-scope="scope">
          <div style="white-space: pre-line; text-align: left;">{{ formatManpower(scope.row) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="工作量（人日）" align="center" min-width="120">
        <template slot-scope="scope">
          <div style="white-space: pre-line; text-align: left;">{{ formatWorkload(scope.row) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="需求背景" align="center" prop="requirementBackground" min-width="160">
        <template slot-scope="scope">
          <div v-if="!scope.row.requirementBackground || scope.row.requirementBackground.length < 30">
            {{ scope.row.requirementBackground }}
          </div>
          <el-tooltip v-else :content="scope.row.requirementBackground" placement="top">
            <div class="ellipsis-multiline">
              {{ scope.row.requirementBackground }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="负责项目经理" align="center" prop="projectManagers" min-width="120" />
      <el-table-column label="完成时间" align="center" prop="completionTime" min-width="120">
        <template slot-scope="scope">
          {{ formatDate(scope.row.completionTime) }}
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="createdBy" min-width="100" />
      <el-table-column label="创建时间" align="center" prop="createdTime" min-width="120">
        <template slot-scope="scope">
          {{ formatDate(scope.row.createdTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="100" fixed="right" class-name="small-padding fixed-width">
        <template v-slot:default="scope">
          <el-button size="mini" type="text" @click="handleEdit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 新增编辑弹窗 -->
    <addEditDialog :dialogVisible.sync="addEditDialog.show" :dialogTitle="addEditDialog.title" :dialogData="addEditDialog.data" @callback="handleQuery"></addEditDialog>
  </div>
</template>

<script>
import { releaseRecordList } from "@/api/system/releaseRecord"
import addEditDialog from "./components/addEditDialog.vue"
export default {
  name: "ReleaseRecord",
  dicts: [
    'project_outcome_types',
    'project_outcome_business_category_major',
    'project_outcome_business_category_minor',
    'project_outcome_project_manager',
    'project_outcome_data_sources',
    'project_outcome_creator',
    'project_outcome_dev_dept',
    'project_outcome_test_dept'
  ],
  components: {
    addEditDialog
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 成果表格数据
      tableData: [
        {id: 1}
      ],
      // 部门字典数据
      devDeptDict: [],
      testDeptDict: [],
      // 数据库下拉数据
      systemOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        resultType: null,
        businessCategoryMajor: null,
        businessCategoryMinor: null,
        projectManager: null,
        dataSource: null,
        createdBy: null,
        createdTimeRange: [], // 创建时间
        startEndTimeRange: [] // 开始时间-结束时间
      },
      addEditDialog: {
        show: false,
        title: '新增',
        data: {}
      }
    }
  },
  created() {
    this.queryParams.createdTimeRange = this.getMonthRange()
    this.loadDeptDicts()
    this.getList()
  },
  methods: {
    /** 查询成果列表 */
    getList () {
      if (this.queryParams.createdTimeRange && this.queryParams.createdTimeRange.length !== 0) { // 创建时间
        this.queryParams.createdTimeStart = this.queryParams.createdTimeRange[0]
        this.queryParams.createdTimeEnd = this.queryParams.createdTimeRange[1]
      } else {
        this.queryParams.createdTimeStart = null
        this.queryParams.createdTimeEnd = null
      }
      if (this.queryParams.startEndTimeRange && this.queryParams.startEndTimeRange.length !== 0) { // 开始时间-结束时间
        this.queryParams.startTime = this.queryParams.startEndTimeRange[0]
        this.queryParams.endTime = this.queryParams.startEndTimeRange[1]
      } else {
        this.queryParams.startTime = null
        this.queryParams.endTime = null
      }
      let params = {...this.queryParams}
      delete params.createdTimeRange
      delete params.startEndTimeRange
      this.loading = true
      releaseRecordList(params).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.total = res.total
          this.tableData = res.rows
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    /** 根据value获取字典label */
    getDictLabel (value, dictData) {
      const item = dictData.find(item => item.value === value)
      return item ? item.label : value
    },
    /** 加载部门字典数据 */
    loadDeptDicts() {
      // 加载开发组字典
      if (this.dict.type.project_outcome_dev_dept) {
        this.devDeptDict = this.dict.type.project_outcome_dev_dept
      }
      // 加载测试组字典
      if (this.dict.type.project_outcome_test_dept) {
        this.testDeptDict = this.dict.type.project_outcome_test_dept
      }
    },
    /** 格式化日期 */
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    /** 获取状态标签 */
    getStatusLabel(status) {
      const statusMap = {
        '1': '未开始',
        '2': '进行中',
        '3': '已完成',
        '4': '已取消'
      }
      return statusMap[status] || status
    },
    /** 格式化项目里程碑 */
    formatMilestone(row) {
      const milestones = []
      if (row.milestoneRequirements) {
        milestones.push(`完成评审：${this.formatDate(row.milestoneRequirements)}`)
      }
      if (row.milestoneDevelopment) {
        milestones.push(`完成开发：${this.formatDate(row.milestoneDevelopment)}`)
      }
      if (row.milestoneTest) {
        milestones.push(`完成测试验收：${this.formatDate(row.milestoneTest)}`)
      }
      if (row.milestoneOnline) {
        milestones.push(`完成上线：${this.formatDate(row.milestoneOnline)}`)
      }
      return milestones.join('\n')
    },
    /** 格式化任务说明/进度 */
    formatProgress(row) {
      const progress = []
      if (row.requirementsProgress !== null && row.requirementsProgress !== undefined) {
        progress.push(`需求评审：${row.requirementsProgress}%`)
      }
      if (row.developmentProgress !== null && row.developmentProgress !== undefined) {
        progress.push(`开发进度：${row.developmentProgress}%`)
      }
      if (row.testProgress !== null && row.testProgress !== undefined) {
        progress.push(`测试验收进度：${row.testProgress}%`)
      }
      return progress.join('\n')
    },
    /** 格式化干系人 */
    formatStakeholders(row) {
      const stakeholders = []
      if (row.productManagers) {
        stakeholders.push(`产品：${row.productManagers}`)
      }
      if (row.devTeams) {
        const devTeamNames = this.convertDeptIdsToNames(row.devTeams, this.devDeptDict)
        stakeholders.push(`开发：${devTeamNames}`)
      }
      if (row.testTeams) {
        const testTeamNames = this.convertDeptIdsToNames(row.testTeams, this.testDeptDict)
        stakeholders.push(`测试：${testTeamNames}`)
      }
      return stakeholders.join('\n')
    },
    /** 格式化投入人力 */
    formatManpower(row) {
      const manpower = []
      if (row.devManpower) {
        manpower.push(`开发：${row.devManpower}人`)
      }
      if (row.testManpower) {
        manpower.push(`测试：${row.testManpower}人`)
      }
      return manpower.join('\n')
    },
    /** 格式化工作量（人日） */
    formatWorkload(row) {
      const workload = []
      if (row.devWorkload) {
        workload.push(`开发：${row.devWorkload}人日`)
      }
      if (row.testWorkload) {
        workload.push(`测试：${row.testWorkload}人日`)
      }
      return workload.join('\n')
    },
    /** 将部门ID转换为部门名称 */
    convertDeptIdsToNames(deptIds, deptDict) {
      if (!deptIds) return ''
      const ids = deptIds.split(',')
      const names = ids.map(id => {
        const dept = deptDict.find(item => item.value === id.trim())
        return dept ? dept.label : id
      })
      return names.join('、')
    },
    /** 添加 */
    handleAdd () {
      this.addEditDialog.show = true
      this.addEditDialog.title = '新增项目成果'
      this.addEditDialog.data = {}
    },
    /** 编辑 */
    handleEdit (row) {
      this.addEditDialog.show = true
      this.addEditDialog.title = '编辑项目成果'
      this.addEditDialog.data = row
    },
    /** 排序 */
    handleSortChange (column, prop, order) {
      this.queryParams.orderByColumn = column.prop
      this.queryParams.isAsc = column.order
      this.handleQuery()
    },
    /** 获取当月时间范围 */
    getMonthRange () {
      const now = new Date()
      // 获取当月第一天
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
      // 获取下个月第一天，然后减1毫秒得到当月最后一天的 23:59:59
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1)
      endOfMonth.setTime(endOfMonth.getTime() - 1)
      // 格式化函数
      function formatDate(date) {
        const y = date.getFullYear();
        const m = String(date.getMonth() + 1).padStart(2, '0')
        const d = String(date.getDate()).padStart(2, '0')
        const h = String(date.getHours()).padStart(2, '0')
        const mm = String(date.getMinutes()).padStart(2, '0')
        const s = String(date.getSeconds()).padStart(2, '0')
        return `${y}-${m}-${d} ${h}:${mm}:${s}`
      }
      return [formatDate(startOfMonth), formatDate(endOfMonth)]
    }
  }
}
</script>
<style>
/* 多行省略核心样式 */
.ellipsis-multiline {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; /* 控制显示行数 */
  line-clamp: 3; /* 标准属性 */
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5; /* 与行高保持一致 */
  max-height: calc(3 * 1.5em); /* 行高*行数 */
  word-break: break-all; /* 允许单词内断行 */
}
.el-tooltip__popper {
  max-width: 600px; /* 可根据实际情况调整 */
}
</style>
